import { create } from 'zustand';

import { getSales, registerSale } from '@/actions/user';
import {
  SalesActions,
  SalesStates,
  SaleUserType,
  GetSalesParams,
  CreateSaleRequest,
} from '@/store/sales/type';

export const useSalesStore = create<SalesStates & SalesActions>(set => ({
  sales: [],
  loading: false,
  creating: false,
  setSales: (sales: SaleUserType[]) => set(() => ({ sales })),
  setLoading: (loading: boolean) => set(() => ({ loading })),
  setCreating: (creating: boolean) => set(() => ({ creating })),
  fetchSales: async (params?: GetSalesParams) => {
    try {
      set(() => ({ loading: true }));
      const response: any = await getSales(params);
      if (response?.status) {
        const sales = Array.isArray(response.data) ? response.data : [];
        set(() => ({ sales, loading: false }));
      } else {
        set(() => ({ sales: [], loading: false }));
      }
    } catch (error) {
      console.error('Failed to fetch sales:', error);
      set(() => ({ sales: [], loading: false }));
    }
  },
  createSale: async (data: CreateSaleRequest) => {
    try {
      set(() => ({ creating: true }));
      const response: any = await registerSale(data);
      if (response?.status) {
        // Add the new sale to the existing sales list
        set(state => ({
          sales: [response.data, ...state.sales],
          creating: false,
        }));
        return response;
      } else {
        set(() => ({ creating: false }));
        return response;
      }
    } catch (error) {
      console.error('Failed to create sale:', error);
      set(() => ({ creating: false }));
      throw error;
    }
  },
}));
